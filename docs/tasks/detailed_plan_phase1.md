# Assumptions:

* Team size: 6 members (mix of FE, BE, AI/ML, Full Stack)
* Capacity per dev per week: \~32 hours (accounting for meetings, testing, review)
* Total capacity per sprint (Phase 1): 6 devs × 32 hrs = 192 hrs/sprint
* Goal for Phase 1: Implement at least 50% of core functionality (frontend + backend + agent flow MVP)

# 📊 Progress Tracking

**Total Progress: 1/47 tasks completed (2%)**

- **Not Started:** 46 tasks
- **In Progress:** 0 tasks  
- **Completed:** 1 task

—

# 📌 Phase 1: 3 Weeks (3 Sprints) – MVP

Goal: Build working pipeline from input → skill sync → roadmap → report (first version)

### 🚀 Sprint 1 (Week 1): Core Input + Skill Sync Flow + Agent Framework

| #   | Task Group       | Task                                             | Est. Hours | Assigned To  | PIC                           | Status      | Step in Pipeline       |
| --- | ---------------- | ------------------------------------------------ | ---------- | ------------ | ----------------------------- | ----------- | ---------------------- |
| 1   | Project Setup    | Setup Frontend scaffolding                       | 3          | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 2   | Project Setup    | Setup Backend scaffolding                        | 3          | Backend Dev  | TruongPH2                     | Not Started |                        |
| 3   | Project Setup    | Setup AI Service scaffolding                     | 3          | AI/ML Dev    | NamNH46                       | Done        |                        |
| 4   | Project Setup    | Setup CI/CD Pipeline                             | 2          | DevOps       | NamNH46                       | Not Started |                        |
| 5   | Research         | Research about RAG                               | 8          | AI/ML Dev    | PhongTN                       | Not Started |                        |
| 6   | Research         | Research about UI/UX                             | 8          | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 7   | Research         | Research about agent gap analysis, agent roadmap | 8          | AI/ML Dev    | QuyetDB, TruongPH2, TrungDD22 | Not Started |                        |
| 8   | Research         | Research about state management in pipeline      | 6          | AI/ML Dev    | PhongTN                       | Not Started |                        |
| 9   | Prototype        | Create prototype for all pages and main scenario | 12         | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 10  | Authentication   | SSO Integration Backend (FSOFT mocked)           | 8          | Backend Dev  | TruongPH2                     | Not Started | User Authentication    |
| 11  | Authentication   | SSO Integration Frontend (UI)                    | 4          | Frontend Dev | DaiNQ11                       | Not Started | User Authentication    |
| 12  | Input Collection | Create Learning Request Form (FE)                | 10         | Frontend Dev | DaiNQ11                       | Not Started | Input Collection       |
| 13  | Input Collection | Backend API: Submit Learning Request             | 10         | Backend Dev  | TruongPH2                     | Not Started | Input Collection       |
| 14  | Skill Sync       | Skill Sync Engine: Connect to mock data          | 16         | Backend Dev  | PhongTN                       | Not Started | Skill Profile Fetching |
| 15  | Skill Sync       | Skill Profile Viewer (FE)                        | 10         | Frontend Dev | DaiNQ11                       | Not Started | Skill Profile Display  |
| 16  | Data Models      | DB Models: Learning Request, Skill Profile       | 6          | Backend Dev  | TruongPH2                     | Not Started |                        |
| 17  | Data Models      | DB Models: User Profile                          | 3          | Backend Dev  | TruongPH2                     | Not Started | User Management        |
| 18  | Agent Flow       | Agent Supervisor Workflow Engine                 | 12         | AI/ML Dev    | NamNH46                       | Not Started | Agent Coordination     |
| 19  | Agent Flow       | Multi-agent Communication Logic                  | 8          | AI/ML Dev    | NamNH46                       | Not Started | Agent Coordination     |

🕒 Sprint Total: \~140 hrs total (includes research phase + agent framework)

---

### 🚀 Sprint 2 (Week 2): Gap Analysis + Roadmap Generation + Initial Report

| #   | Task Group        | Task                                             | Est. Hours | Assigned To  | Status      | Step in Pipeline       |
| --- | ----------------- | ------------------------------------------------ | ---------- | ------------ | ----------- | ---------------------- |
| 1   | Gap Analysis      | Build Gap Analysis Engine (Backend Logic)        | 10         | Backend Dev  | Not Started | Gap Analysis           |
| 2   | Gap Analysis      | Build Gap Analysis Engine (AI Logic)             | 8          | AI/ML Dev    | Not Started | Gap Analysis           |
| 3   | Gap Analysis      | Build Gap Analysis Engine (Frontend UI)          | 8          | Frontend Dev | Not Started | Gap Analysis Display   |
| 4   | Target Profile    | Build RAG-based Target Profile Parser            | 12         | AI/ML Dev    | Not Started | Target Profile Gen     |
| 5   | Gap Analysis      | Store & view Gap Results (Backend)               | 4          | Backend Dev  | Not Started | Gap Analysis Display   |
| 6   | Gap Analysis      | Store & view Gap Results (Frontend)              | 4          | Frontend Dev | Not Started | Gap Analysis Display   |
| 7   | Roadmap           | Learning Roadmap Generator (AI Logic)            | 10         | AI/ML Dev    | Not Started | Roadmap Generation     |
| 8   | Roadmap           | Learning Roadmap Generator (Backend API)         | 8          | Backend Dev  | Not Started | Roadmap Generation     |
| 9   | Roadmap           | Display Roadmap Viewer (timeline basic)          | 12         | Frontend Dev | Not Started | Roadmap Display        |
| 10  | Data Models       | DB Models: Gap Analysis, Target Profile, Roadmap | 6          | Backend Dev  | Not Started |                        |
| 11  | Data Models       | DB Models: Learning Resource, Interaction Log    | 4          | Backend Dev  | Not Started |                        |
| 12  | Validation        | Input Validation & Error Handling                | 6          | Backend Dev  | Not Started | Data Quality           |
| 13  | Report Generation | Agent Report Generator (Markdown)                | 10         | AI/ML Dev    | Not Started | Report Generation      |
| 14  | Report Generation | Report Viewer (FE)                               | 8          | Frontend Dev | Not Started | Report Display         |
| 15  | Report Generation | Export to PDF (Backend Logic)                    | 4          | Backend Dev  | Not Started | Report Export          |
| 16  | Skill Sync        | Skill Sync Scheduler (weekly/manual)             | 8          | Backend Dev  | Not Started | Skill Profile Fetching |
| 17  | API Development   | API: Fetch Gap/Roadmap                           | 6          | Backend Dev  | Not Started |                        |

🕒 Sprint Total: \~128 hrs

---

### 🚀 Sprint 3 (Week 3): Report Editing + Version Management + System Features + QA

| #   | Task Group         | Task                                    | Est. Hours | Assigned To   | Status      | Step in Pipeline     |
| --- | ------------------ | --------------------------------------- | ---------- | ------------- | ----------- | -------------------- |
| 1   | Report Editing     | Agent Report Editor Backend             | 6          | Backend Dev   | Not Started | Report Editing       |
| 2   | Report Editing     | Agent Report Editor Frontend            | 4          | Frontend Dev  | Not Started | Report Editing       |
| 3   | Roadmap Editing    | Roadmap Edit UI                         | 10         | Frontend Dev  | Not Started | Roadmap Editing      |
| 4   | Version Management | Sync Skill Change Trigger → New Version | 6          | Backend Dev   | Not Started | Version Management   |
| 5   | User Communication | Notification for roadmap version        | 4          | Backend Dev   | Not Started | User Communication   |
| 6   | Log Display        | Interaction Log Viewer                  | 6          | Frontend Dev  | Not Started | Log Display          |
| 7   | Interaction Log    | Agent Interaction Log (basic)           | 8          | Backend Dev   | Not Started |                      |
| 8   | Version Control    | Learning Request Versioning             | 4          | Backend Dev   | Not Started | Version Management   |
| 9   | Version Control    | Gap Analysis History Storage            | 4          | Backend Dev   | Not Started | Version Management   |
| 10  | Search & Filter    | Basic Search in Interaction Logs        | 6          | Backend Dev   | Not Started | Log Management       |
| 11  | Configuration      | Admin Settings Panel (basic)            | 8          | Full Stack    | Not Started | System Configuration |
| 12  | Quality Assurance  | Unit + Integration Testing              | 8          | All           | Not Started |                      |
| 13  | Quality Assurance  | Refactor SonarQube issues               | 6          | All           | Not Started |                      |
| 14  | Quality Assurance  | Unit test coverage                      | 8          | All (rotated) | Not Started |                      |
| 15  | Quality Assurance  | Final QA & Bug Fix Round                | 8          | All           | Not Started |                      |

🕒 Sprint Total: \~96 hrs (includes testing and QA)